# 生产环境配置文件
# 注意：此文件包含敏感信息，不应提交到版本控制系统

# Flask 环境配置
FLASK_ENV=production
FLASK_DEBUG=False

# 安全配置 - 生产环境必须更改
SECRET_KEY=CHANGE-THIS-IN-PRODUCTION-TO-A-SECURE-RANDOM-STRING

# 数据库配置 - 从原docker-compose.yml提取
DATABASE_URL=mysql+pymysql://root:rw80827@114.96.68.254/model_registry

# 管理员配置 - 从原docker-compose.yml提取
ADMIN_PASSWORD=@Rw80827

# API 配置
API_ENABLED=true
API_VERSION=v1

# 安全设置 - 生产环境更严格
MAX_LOGIN_ATTEMPTS=3
LOGIN_ATTEMPT_TIMEOUT=600  # 10分钟

# 分页配置
ITEMS_PER_PAGE=20

# 日志配置 - 生产环境使用WARNING级别
LOG_LEVEL=WARNING

# 会话配置 - 生产环境安全设置
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Strict

# 数据库连接池配置 - 生产环境优化
DB_POOL_SIZE=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
DB_POOL_PRE_PING=true

# 速率限制配置 - 生产环境启用
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT=50
RATE_LIMIT_WINDOW=3600

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=/app/uploads

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=600

# 监控配置
MONITORING_ENABLED=true
METRICS_ENDPOINT=/metrics

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400  # 24小时
BACKUP_RETENTION=30    # 保留30天

# 预设平台配置 - 生产环境可能需要更新API密钥
INIT_PLATFORMS_ENABLED=true

# Spark平台配置
SPARK_PLATFORM_NAME=spark
SPARK_BASE_URL=https://spark-api-open.xf-yun.com/v1
SPARK_API_KEY=fb4d51d6a4e8802f6c0febf24ac75d58:YmNhZWY2YmI0MDU0ZTU2YjRjMTdhMGU1

# OpenRouter平台配置
OPENROUTER_PLATFORM_NAME=OpenRouter
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=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

# DeepBricks平台配置
DEEPBRICKS_PLATFORM_NAME=deepbricks
DEEPBRICKS_BASE_URL=https://api.deepbricks.ai/v1/
DEEPBRICKS_API_KEY=sk-pmLx4UQDPWYhQYJqc8MQgbQ6pkmkRoQ6YxTumIy5nQWjSnIR

# 应用运行配置
FLASK_RUN_HOST=0.0.0.0
FLASK_RUN_PORT=5000

# Docker数据库配置
DB_ROOT_PASSWORD=CHANGE-THIS-ROOT-PASSWORD
DB_NAME=model_registry
DB_USER=app_user
DB_PASSWORD=CHANGE-THIS-APP-PASSWORD
DB_PORT=3306

# HTTPS配置（生产环境推荐）
PREFERRED_URL_SCHEME=https
