#!/usr/bin/env python3
"""
测试CSRF token修复的脚本
"""

import requests
from bs4 import BeautifulSoup
import re

def test_csrf_token():
    """测试CSRF token是否正常工作"""
    base_url = "http://localhost:5000"
    
    # 创建会话
    session = requests.Session()
    
    print("🔍 测试CSRF token修复...")
    
    # 1. 获取登录页面
    print("\n1. 获取登录页面...")
    login_url = f"{base_url}/auth/login"
    response = session.get(login_url)
    
    if response.status_code != 200:
        print(f"❌ 无法访问登录页面，状态码: {response.status_code}")
        return False
    
    print(f"✅ 登录页面访问成功，状态码: {response.status_code}")
    
    # 2. 解析HTML，查找CSRF token
    print("\n2. 检查CSRF token...")
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 查找hidden的csrf_token字段
    csrf_token_input = soup.find('input', {'name': 'csrf_token', 'type': 'hidden'})
    
    if not csrf_token_input:
        print("❌ 未找到CSRF token隐藏字段")
        return False
    
    csrf_token = csrf_token_input.get('value')
    if not csrf_token:
        print("❌ CSRF token值为空")
        return False
    
    print(f"✅ 找到CSRF token: {csrf_token[:20]}...")
    
    # 3. 测试不带CSRF token的登录请求（应该失败）
    print("\n3. 测试不带CSRF token的登录请求...")
    login_data_no_csrf = {
        'password': 'admin123'
    }
    
    response_no_csrf = session.post(login_url, data=login_data_no_csrf)
    
    if "csrf" in response_no_csrf.text.lower() or "token" in response_no_csrf.text.lower():
        print("✅ 不带CSRF token的请求被正确拒绝")
    else:
        print("⚠️  不带CSRF token的请求未被拒绝（可能CSRF保护未启用）")
    
    # 4. 测试带正确CSRF token的登录请求
    print("\n4. 测试带正确CSRF token的登录请求...")
    login_data_with_csrf = {
        'csrf_token': csrf_token,
        'password': 'admin123'
    }
    
    response_with_csrf = session.post(login_url, data=login_data_with_csrf)
    
    # 检查是否重定向（登录成功的标志）
    if response_with_csrf.status_code == 302 or "登录成功" in response_with_csrf.text:
        print("✅ 带CSRF token的登录请求成功")
        return True
    elif "密码错误" in response_with_csrf.text:
        print("✅ CSRF token验证通过，但密码错误（这是正常的，因为我们使用了默认密码）")
        return True
    else:
        print(f"❌ 带CSRF token的登录请求失败，状态码: {response_with_csrf.status_code}")
        print(f"响应内容: {response_with_csrf.text[:500]}...")
        return False

def test_form_structure():
    """测试表单结构是否正确"""
    print("\n🔍 测试表单结构...")
    
    session = requests.Session()
    response = session.get("http://localhost:5000/auth/login")
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 检查表单是否存在
    form = soup.find('form')
    if not form:
        print("❌ 未找到登录表单")
        return False
    
    print("✅ 找到登录表单")
    
    # 检查表单方法
    method = form.get('method', '').lower()
    if method != 'post':
        print(f"❌ 表单方法不正确: {method}")
        return False
    
    print("✅ 表单方法正确: POST")
    
    # 检查密码字段
    password_field = form.find('input', {'name': 'password', 'type': 'password'})
    if not password_field:
        print("❌ 未找到密码字段")
        return False
    
    print("✅ 找到密码字段")
    
    # 检查CSRF token字段
    csrf_field = form.find('input', {'name': 'csrf_token', 'type': 'hidden'})
    if not csrf_field:
        print("❌ 未找到CSRF token字段")
        return False
    
    print("✅ 找到CSRF token字段")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试CSRF token修复...")
    
    # 测试表单结构
    form_ok = test_form_structure()
    
    # 测试CSRF token功能
    csrf_ok = test_csrf_token()
    
    print("\n" + "="*50)
    if form_ok and csrf_ok:
        print("🎉 所有测试通过！CSRF token修复成功！")
        print("✅ 登录表单现在包含正确的CSRF token")
        print("✅ CSRF保护正常工作")
    else:
        print("❌ 测试失败，需要进一步检查")
    
    print("="*50)
