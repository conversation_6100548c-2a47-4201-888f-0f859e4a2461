services:
  model-registry:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: model-registry-local
    restart: unless-stopped
    ports:
      - "${FLASK_RUN_PORT:-5000}:5000"
    env_file:
      - .env.local  # 本地环境配置文件
    environment:
      FLASK_ENV: development
      PYTHONUNBUFFERED: 1
    volumes:
      - ./app:/app  # 开发时挂载代码目录
      - logs:/app/logs  # 使用命名卷存储日志
      - uploads:/app/uploads  # 使用命名卷存储上传文件
    networks:
      - model-registry-network
    depends_on:
      - db

  db:
    image: mysql:8.0
    container_name: model-registry-db-local
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${DB_NAME:-model_registry}
      MYSQL_USER: ${DB_USER:-app_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-app_password}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - db_data_local:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backup:/backup  # 添加备份目录挂载
    networks:
      - model-registry-network
    command: --default-authentication-plugin=mysql_native_password

networks:
  model-registry-network:
    driver: bridge

volumes:
  db_data_local:
    driver: local
  logs:
    driver: local
  uploads:
    driver: local
