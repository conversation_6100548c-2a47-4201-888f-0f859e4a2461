# 本地开发环境配置文件
# 用于连接本地数据库

# Flask 环境配置
FLASK_ENV=development
FLASK_DEBUG=True

# 安全配置
SECRET_KEY=local-dev-secret-key-not-for-production

# 本地数据库配置
DATABASE_URL=mysql+pymysql://root:rootpassword@localhost:3306/model_registry

# 管理员配置
ADMIN_PASSWORD=admin123

# API 配置
API_ENABLED=true
API_VERSION=v1

# 安全设置 - 开发环境较宽松
MAX_LOGIN_ATTEMPTS=5
LOGIN_ATTEMPT_TIMEOUT=300  # 5分钟

# 分页配置
ITEMS_PER_PAGE=20

# 日志配置 - 开发环境使用DEBUG级别
LOG_LEVEL=DEBUG

# 会话配置 - 开发环境设置
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# 数据库连接池配置 - 开发环境
DB_POOL_SIZE=5
DB_POOL_TIMEOUT=20
DB_POOL_RECYCLE=3600
DB_POOL_PRE_PING=true

# 速率限制配置 - 开发环境较宽松
RATE_LIMIT_ENABLED=false
RATE_LIMIT_DEFAULT=100
RATE_LIMIT_WINDOW=3600

# 文件上传配置
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=./uploads

# 缓存配置
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# 监控配置
MONITORING_ENABLED=false
METRICS_ENDPOINT=/metrics

# 备份配置
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400  # 24小时
BACKUP_RETENTION=7     # 保留7天

# 预设平台配置
INIT_PLATFORMS_ENABLED=true

# Spark平台配置
SPARK_PLATFORM_NAME=spark
SPARK_BASE_URL=https://spark-api-open.xf-yun.com/v1
SPARK_API_KEY=fb4d51d6a4e8802f6c0febf24ac75d58:YmNhZWY2YmI0MDU0ZTU2YjRjMTdhMGU1

# OpenRouter平台配置
OPENROUTER_PLATFORM_NAME=OpenRouter
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=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

# DeepBricks平台配置
DEEPBRICKS_PLATFORM_NAME=deepbricks
DEEPBRICKS_BASE_URL=https://api.deepbricks.ai/v1/
DEEPBRICKS_API_KEY=sk-pmLx4UQDPWYhQYJqc8MQgbQ6pkmkRoQ6YxTumIy5nQWjSnIR

# 应用运行配置
FLASK_RUN_HOST=0.0.0.0
FLASK_RUN_PORT=5000

# Docker数据库配置
DB_ROOT_PASSWORD=rootpassword
DB_NAME=model_registry
DB_USER=app_user
DB_PASSWORD=app_password
DB_PORT=3306

# 开发环境设置
PREFERRED_URL_SCHEME=http
SQLALCHEMY_ECHO=false
