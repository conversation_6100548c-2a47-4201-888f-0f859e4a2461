
services:
  model-registry:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: model-registry
    restart: unless-stopped
    ports:
      - "${FLASK_RUN_PORT:-5000}:5000"
    env_file:
      - .env.production  # 生产环境配置文件
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
    volumes:
      - logs:/app/logs  # 使用命名卷存储日志
      - uploads:/app/uploads  # 使用命名卷存储上传文件
    networks:
      - model-registry-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - db

  db:
    image: mysql:8.0
    container_name: model-registry-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-localpassword}
      MYSQL_DATABASE: ${DB_NAME:-model_registry}
      MYSQL_USER: ${DB_USER:-app_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-localpassword}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backup:/backup  # 添加备份目录挂载
    networks:
      - model-registry-network
    command: --default-authentication-plugin=mysql_native_password

networks:
  model-registry-network:
    driver: bridge

volumes:
  db_data:
    driver: local
  logs:
    driver: local
  uploads:
    driver: local
