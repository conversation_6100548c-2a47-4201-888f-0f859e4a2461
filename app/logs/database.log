2025-07-12 12:35:17,594 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:35:17,594 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,597 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:35:17,597 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,597 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:35:17,598 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,598 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,599 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:35:17,599 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,605 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:35:17,606 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,607 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:35:17,608 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,609 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:35:17,609 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,611 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:35:17,636 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:35:17,636 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT DATABASE()
2025-07-12 12:35:17,637 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,637 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@sql_mode
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:35:17,638 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT @@lower_case_table_names
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,639 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`platforms`
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,640 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`ai_models`
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,642 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`applications`
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,643 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - DESCRIBE `model_registry`.`app_models`
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,645 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [raw sql] {}
2025-07-12 12:35:17,647 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:35:17,647 - sqlalchemy.engine.Engine - INFO - base.py:2704 - _connection_commit_impl - COMMIT
2025-07-12 12:35:17,657 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,657 - sqlalchemy.engine.Engine - INFO - base.py:2698 - _connection_begin_impl - BEGIN (implicit)
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT platforms.id AS platforms_id, platforms.name AS platforms_name, platforms.base_url AS platforms_base_url, platforms.api_key AS platforms_api_key, platforms.created_at AS platforms_created_at, platforms.updated_at AS platforms_updated_at 
FROM platforms
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - SELECT platforms.id AS platforms_id, platforms.name AS platforms_name, platforms.base_url AS platforms_base_url, platforms.api_key AS platforms_api_key, platforms.created_at AS platforms_created_at, platforms.updated_at AS platforms_updated_at 
FROM platforms
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00028s] {}
2025-07-12 12:35:17,659 - sqlalchemy.engine.Engine - INFO - base.py:1842 - _execute_context - [generated in 0.00028s] {}
2025-07-12 12:35:17,660 - sqlalchemy.engine.Engine - INFO - base.py:2701 - _connection_rollback_impl - ROLLBACK
2025-07-12 12:35:17,660 - sqlalchemy.engine.Engine - INFO - base.py:2701 - _connection_rollback_impl - ROLLBACK
