{% extends "base.html" %}

{% block title %}管理应用模型 - 大语言模型管理系统{% endblock %}

{% block header %}管理应用 "{{ application.name }}" 的模型{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('manage_app_models', id=application.id) }}">
            <div class="mb-3">
                <label class="form-label">选择可用模型</label>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>选择</th>
                                <th>默认</th>
                                <th>ID</th>
                                <th>模型名称</th>
                                <th>平台</th>
                                <th>输入价格</th>
                                <th>输出价格</th>
                                <th>可见</th>
                                <th>免费</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for model in all_models %}
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input model-checkbox" type="checkbox" 
                                               name="model_ids" value="{{ model.id }}" id="model{{ model.id }}"
                                               {% if model.id in current_model_ids %}checked{% endif %}>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input default-radio" type="radio" 
                                               name="default_model_id" value="{{ model.id }}" id="default{{ model.id }}"
                                               {% if model.id == default_model_id %}checked{% endif %}
                                               {% if model.id not in current_model_ids %}disabled{% endif %}>
                                    </div>
                                </td>
                                <td>{{ model.id }}</td>
                                <td>{{ model.display_name }}</td>
                                <td>{{ model.platform.name }}</td>
                                <td>${{ "%.6f"|format(model.input_token_price) }}</td>
                                <td>${{ "%.6f"|format(model.output_token_price) }}</td>
                                <td>
                                    {% if model.is_visible_model %}
                                    <span class="badge bg-success">是</span>
                                    {% else %}
                                    <span class="badge bg-secondary">否</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if model.free %}
                                    <span class="badge bg-success">是</span>
                                    {% else %}
                                    <span class="badge bg-secondary">否</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('list_applications') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 当选择模型复选框变化时更新默认模型单选框状态
    $(document).ready(function() {
        $('.model-checkbox').change(function() {
            var modelId = $(this).val();
            var defaultRadio = $('#default' + modelId);

            if ($(this).is(':checked')) {
                defaultRadio.prop('disabled', false);
            } else {
                defaultRadio.prop('disabled', true);
                defaultRadio.prop('checked', false);
            }
        });
    });
</script>
{% endblock %}
